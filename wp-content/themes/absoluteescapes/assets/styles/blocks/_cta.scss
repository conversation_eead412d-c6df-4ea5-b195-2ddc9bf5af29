// CTA Block Styles
// Uses existing listing-cta classes with curve overrides
// Higher specificity selectors to override the base .listing-cta__background::after

.listing-cta.cta-block {
    // Base background - override default ::after with maximum specificity
    .listing-cta__background {
        // Reset the default curved edge with high specificity
        &::after {
            display: none !important;
            content: none !important;
        }

        // Only apply default padding, no background color
        padding: 40px 0 !important;
        background-color: transparent;
    }

    // Curved edge at bottom - uses ::after with maximum specificity
    .listing-cta__background.curve-bottom {
        padding: 40px 0 80px 0 !important;

        &::after {
            content: '' !important;
            display: block !important;
            position: absolute !important;
            bottom: -2px !important;
            top: auto !important;
            left: 0 !important;
            width: 100% !important;
            height: 40px !important;
            background-image: url('../img/banner-mask.svg') !important;
            background-size: 100% 100% !important;
            background-repeat: no-repeat !important;
            background-position: bottom center !important;
            transform: none !important;
            z-index: 1 !important;
        }
    }

    // Curved edge at top - uses ::before with maximum specificity
    .listing-cta__background.curve-top {
        padding: 80px 0 40px 0 !important;

        // Ensure the default ::after is completely hidden
        &::after {
            display: none !important;
            content: none !important;
        }

        // Add the top curve with ::before
        &::before {
            content: '' !important;
            display: block !important;
            position: absolute !important;
            top: -2px !important;
            bottom: auto !important;
            left: 0 !important;
            width: 100% !important;
            height: 40px !important;
            background-image: url('../img/banner-mask.svg') !important;
            background-size: 100% 100% !important;
            background-repeat: no-repeat !important;
            background-position: bottom center !important;
            transform: scaleY(-1) !important;
            z-index: 1 !important;
        }
    }
}
