// CTA Block Styles
// Extends the existing listing-cta styles for use in page builder
// Uses more specific selectors to override the base listing-cta styles

.listing-cta.cta-block {
    margin: 60px 0;

    .listing-cta__background {
        position: relative;
        padding: 40px 0;

        // Override the base listing-cta rule - no curved edge by default
        &::after {
            display: none !important;
        }
    }

    // Centered text variant when no image is used
    .listing-cta__text.centre {
        text-align: center;
    }
}

// Curved edge at bottom
.listing-cta.cta-block.cta-block--curved-bottom {
    .listing-cta__background {
        padding-bottom: 80px;

        &::after {
            content: '' !important;
            display: block !important;
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 40px;
            background-image: url('../img/banner-mask.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom center;
            transform: none !important;
            z-index: 1;
        }
    }
}

// Curved edge at top
.listing-cta.cta-block.cta-block--curved-top {
    .listing-cta__background {
        padding-top: 80px;

        &::after {
            content: '' !important;
            display: block !important;
            position: absolute;
            top: -2px;
            bottom: auto !important;
            left: 0;
            width: 100%;
            height: 40px;
            background-image: url('../img/banner-mask.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            background-position: bottom center;
            transform: scaleY(-1) !important;
            z-index: 1;
        }
    }
}
