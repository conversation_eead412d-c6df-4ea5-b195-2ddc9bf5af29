// CTA Block Styles
// Independent styles that don't conflict with existing listing-cta styles

.cta-block-wrapper {
    margin: 60px 0;
}

.cta-block-background {
    position: relative;
    padding: 40px 0;
    width: 100%;
}

.cta-block-content {
    max-width: 1000px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 60px;

    @media (max-width: 767px) {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
}

.cta-block-image {
    flex: 0 0 200px;

    @media (max-width: 767px) {
        flex: 0 0 auto;
    }

    img {
        width: 100%;
        max-width: 200px;
        height: auto;
        display: block;
        border-radius: 50%;
    }
}

.cta-block-text {
    flex: 1;

    .button {
        margin-right: 30px;

        @media (max-width: 455px) {
            margin: 0 auto;
        }
    }

    .svg-inline--fa {
        transform: scale(1.33);
        margin-right: 10px;
    }

    // Centered text variant when no image is used
    &.centre {
        text-align: center;
    }
}

.cta-block-actions {
    display: flex;
    flex-direction: row;
    align-items: center;

    @media (max-width: 455px) {
        flex-direction: column;
        gap: 25px;
    }
}

// Curved edge at bottom
.curve-bottom {
    padding-bottom: 80px;

    &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background-image: url('../img/banner-mask.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: bottom center;
        z-index: 1;
    }
}

// Curved edge at top
.curve-top {
    padding-top: 80px;

    &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background-image: url('../img/banner-mask.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: bottom center;
        transform: scaleY(-1);
        z-index: 1;
    }
}
