// CTA Block Styles
// Extends the existing listing-cta styles for use in page builder

.listing-cta {
    &.cta-block {
        margin: 60px 0;
        
        .listing-cta__background {
            background-color: #f5eced;
            padding: 40px 0 80px;
            
            &::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 40px;
                background-image: url('../img/banner-mask.svg');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
                z-index: 1;
            }
        }
        
        // Alternative background color option
        &.cta-block--alt {
            .listing-cta__background {
                background-color: #eaf2f1;
                padding: 80px 0 40px;

                &::after {
                    bottom: auto;
                    top: -2px;
                    transform: scaleY(-1);
                }
            }
        }
        
        // Centered text variant when no image is used
        .listing-cta__text.centre {
            text-align: center;
        }
    }
}
