// CTA Block Styles
// Simple curve implementation for the CTA block

.listing-cta.cta-block {
    // Override the default curve from base styles
    .listing-cta__background {
        &::after {
            display: none;
        }
    }
}

// Simple curve classes that can be added to any element
.curve-bottom {
    position: relative;
    padding-bottom: 80px;

    &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background-image: url('../img/banner-mask.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: bottom center;
        z-index: 1;
    }
}

.curve-top {
    position: relative;
    padding-top: 80px;

    &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background-image: url('../img/banner-mask.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: bottom center;
        transform: scaleY(-1);
        z-index: 1;
    }
}
