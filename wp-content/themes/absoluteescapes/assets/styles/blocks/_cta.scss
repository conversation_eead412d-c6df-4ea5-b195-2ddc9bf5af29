// CTA Block Styles
// Uses existing listing-cta classes with curve overrides

.listing-cta.cta-block {
    // Override the default ::after from the base listing-cta styles
    .listing-cta__background {
        // Reset the default curved edge
        &::after {
            display: none;
        }

        // Only apply default padding, no background color
        padding: 40px 0;
        background-color: transparent;
    }
}

// Curved edge at bottom - uses ::after
.listing-cta__background.curve-bottom {
    padding-bottom: 80px !important;

    &::after {
        content: '' !important;
        display: block !important;
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background-image: url('../img/banner-mask.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: bottom center;
        transform: none !important;
        z-index: 1;
    }
}

// Curved edge at top - uses ::before
.listing-cta__background.curve-top {
    padding-top: 80px !important;

    // Hide the default ::after
    &::after {
        display: none !important;
    }

    // Add the top curve with ::before
    &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 0;
        width: 100%;
        height: 40px;
        background-image: url('../img/banner-mask.svg');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: bottom center;
        transform: scaleY(-1);
        z-index: 1;
    }
}
