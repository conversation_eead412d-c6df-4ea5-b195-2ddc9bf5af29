// CTA Block Styles
// Extends the existing listing-cta styles for use in page builder

.listing-cta {
    &.cta-block {
        margin: 60px 0;

        .listing-cta__background {
            position: relative;
            padding: 40px 0;

            // Default: no curved edge
            &::after {
                display: none;
            }
        }

        // Curved edge at bottom
        &.cta-block--curved-bottom {
            .listing-cta__background {
                padding-bottom: 80px;

                &::after {
                    content: '';
                    display: block;
                    position: absolute;
                    bottom: -2px;
                    left: 0;
                    width: 100%;
                    height: 40px;
                    background-image: url('../img/banner-mask.svg');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: bottom center;
                    z-index: 1;
                }
            }
        }

        // Curved edge at top
        &.cta-block--curved-top {
            .listing-cta__background {
                padding-top: 80px;

                &::after {
                    content: '';
                    display: block;
                    position: absolute;
                    top: -2px;
                    left: 0;
                    width: 100%;
                    height: 40px;
                    background-image: url('../img/banner-mask.svg');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: bottom center;
                    transform: scaleY(-1);
                    z-index: 1;
                }
            }
        }

        // Centered text variant when no image is used
        .listing-cta__text.centre {
            text-align: center;
        }
    }
}
